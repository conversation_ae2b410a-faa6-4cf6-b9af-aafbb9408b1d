// سكريپت لإضافة البراندات إلى Firebase من المتصفح
// انسخ هذا الكود وشغله في Console المتصفح

async function addBrandsToFirebase() {
  console.log('🔄 بدء إضافة البراندات إلى Firebase...');
  
  const brands = [
    {
      id: 'ray-ban',
      name: 'Ray-Ban',
      description: 'براند عالمي مشهور للنظارات الشمسية والطبية',
      image: 'assets/images/brands/rayban.png',
      isActive: true,
      sortOrder: 1,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 'oakley',
      name: 'Oak<PERSON>',
      description: 'براند رياضي للنظارات عالية الأداء',
      image: 'assets/images/brands/oakley.png',
      isActive: true,
      sortOrder: 2,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 'acuvue',
      name: 'Acuvue',
      description: 'براند عالمي للعدسات اللاصقة',
      image: 'assets/images/brands/acuvue.png',
      isActive: true,
      sortOrder: 3,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 'gucci',
      name: 'Gucci',
      description: 'براند إيطالي فاخر للنظارات',
      image: 'assets/images/brands/gucci.png',
      isActive: true,
      sortOrder: 4,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 'prada',
      name: 'Prada',
      description: 'براند إيطالي راقي للنظارات الفاخرة',
      image: 'assets/images/brands/prada.png',
      isActive: true,
      sortOrder: 5,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  ];

  let addedCount = 0;
  let existingCount = 0;

  for (const brand of brands) {
    try {
      // التحقق من وجود البراند
      const existingDoc = await firebase.firestore().collection('brands').doc(brand.id).get();
      
      if (existingDoc.exists) {
        console.log(`⚠️ البراند ${brand.name} موجود مسبقاً`);
        existingCount++;
      } else {
        // إضافة البراند الجديد
        await firebase.firestore().collection('brands').doc(brand.id).set(brand);
        console.log(`✅ تم إضافة البراند ${brand.name}`);
        addedCount++;
      }
    } catch (error) {
      console.error(`❌ خطأ في إضافة البراند ${brand.name}:`, error);
    }
  }

  console.log('');
  console.log('🎉 تم الانتهاء من إضافة البراندات:');
  console.log(`✅ تم إضافة: ${addedCount} براند جديد`);
  console.log(`⚠️ موجود مسبقاً: ${existingCount} براند`);
  console.log(`📊 إجمالي البراندات: ${brands.length} براند`);
  
  return { success: true, added: addedCount, existing: existingCount, total: brands.length };
}

// تشغيل الدالة
addBrandsToFirebase();
